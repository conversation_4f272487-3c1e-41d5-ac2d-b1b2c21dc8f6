'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, AlertCircle, Cable, Save, Info, Bug } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'
import CaviDebugDialog from '@/components/debug/CaviDebugDialog'

interface AggiungiCaviDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface CavoConMetri extends Cavo {
  metri_inseriti?: number
  _isIncompatible?: boolean
}

export default function AggiungiCaviDialog({
  open,
  onClose,
  bobina,
  onSuccess,
  onError
}: AggiungiCaviDialogProps) {
  const { cantiere } = useAuth()
  const [loading, setLoading] = useState(false)
  const [caviLoading, setCaviLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  // Stati per i cavi
  const [caviCompatibili, setCaviCompatibili] = useState<CavoConMetri[]>([])
  const [caviIncompatibili, setCaviIncompatibili] = useState<CavoConMetri[]>([])
  const [caviSelezionati, setCaviSelezionati] = useState<CavoConMetri[]>([])
  const [caviMetri, setCaviMetri] = useState<Record<string, string>>({})
  
  // Stati per validazione
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [warnings, setWarnings] = useState<Record<string, string>>({})
  
  // Debug
  const [showDebugDialog, setShowDebugDialog] = useState(false)

  // Utility per verificare compatibilità tra cavo e bobina (dalla webapp originale)
  const isCompatible = (cavo: any, bobina: any) => {
    return cavo.tipologia === bobina.tipologia &&
           String(cavo.sezione) === String(bobina.sezione)
  }

  // Utility per verificare se un cavo è installato (dalla webapp originale)
  const isCableInstalled = (cavo: any) => {
    return cavo.stato_installazione?.toLowerCase() === 'installato'
  }

  // TEST DIRETTO: Carica i cavi e mostra tutto
  const loadCavi = async () => {
    if (!cantiere) {
      console.log('❌ NESSUN CANTIERE')
      return
    }

    try {
      setCaviLoading(true)
      console.log('🔍 INIZIO TEST - Chiamata API...')

      const caviData = await caviApi.getCavi(cantiere.id_cantiere)
      console.log('✅ API RISPOSTA:', caviData)
      console.log('📊 TOTALE CAVI:', caviData?.length || 0)

      if (!caviData || caviData.length === 0) {
        console.log('❌ NESSUN CAVO RICEVUTO')
        setCaviCompatibili([])
        setCaviIncompatibili([])
        return
      }

      // TEST: Cerca C013 specificamente
      const cavoC013 = caviData.find(cavo => cavo.id_cavo === 'C013')
      console.log('🔍 CAVO C013:', cavoC013 || 'NON TROVATO')

      // TEST: Mostra primi 3 cavi per debug
      console.log('🔍 PRIMI 3 CAVI:', caviData.slice(0, 3))

      // FILTRO SEMPLICE: solo non installati
      const caviNonInstallati = caviData.filter(cavo => {
        const nonInstallato = cavo.stato_installazione?.toLowerCase() !== 'installato'
        console.log(`Cavo ${cavo.id_cavo}: stato=${cavo.stato_installazione}, nonInstallato=${nonInstallato}`)
        return nonInstallato
      })

      console.log('📊 CAVI NON INSTALLATI:', caviNonInstallati.length)

      // COMPATIBILITÀ SEMPLICE
      const compatibili = caviNonInstallati.filter(cavo => {
        const match = cavo.tipologia === bobina?.tipologia && cavo.sezione === bobina?.sezione
        console.log(`Cavo ${cavo.id_cavo}: tip=${cavo.tipologia} vs ${bobina?.tipologia}, sez=${cavo.sezione} vs ${bobina?.sezione}, match=${match}`)
        return match
      })

      const incompatibili = caviNonInstallati.filter(cavo => {
        return !(cavo.tipologia === bobina?.tipologia && cavo.sezione === bobina?.sezione)
      })

      console.log('✅ RISULTATO FINALE:')
      console.log('- Compatibili:', compatibili.length)
      console.log('- Incompatibili:', incompatibili.length)

      setCaviCompatibili(compatibili)
      setCaviIncompatibili(incompatibili)

    } catch (error: any) {
      console.error('❌ ERRORE API:', error)
      onError('Errore nel caricamento dei cavi: ' + error.message)
    } finally {
      setCaviLoading(false)
    }
  }

  // Reset quando si apre il dialog
  useEffect(() => {
    if (open && bobina && cantiere) {
      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      setWarnings({})
      loadCavi()
    }
  }, [open, bobina, cantiere])

  // Gestisce la selezione di un cavo
  const handleCavoToggle = (cavo: CavoConMetri, isCompatible: boolean) => {
    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)

    if (isSelected) {
      // Rimuovi dalla selezione
      setCaviSelezionati(prev => prev.filter(c => c.id_cavo !== cavo.id_cavo))
      setCaviMetri(prev => {
        const newMetri = { ...prev }
        delete newMetri[cavo.id_cavo]
        return newMetri
      })
    } else {
      // Aggiungi alla selezione
      setCaviSelezionati(prev => [...prev, cavo])
      // Imposta metri teorici come default
      setCaviMetri(prev => ({
        ...prev,
        [cavo.id_cavo]: cavo.metri_teorici?.toString() || '0'
      }))

      // Se il cavo è incompatibile, mostra un avviso
      if (!isCompatible) {
        onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione`)
      }
    }
  }



  // Gestisce il cambio dei metri
  const handleMetriChange = (cavoId: string, value: string) => {
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }))

    // Validazione in tempo reale
    validateMetri(cavoId, value)
  }

  // Valida i metri inseriti
  const validateMetri = (cavoId: string, value: string) => {
    const cavo = caviSelezionati.find(c => c.id_cavo === cavoId)
    if (!cavo) return

    const metri = parseFloat(value)
    const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0')

    const newErrors = { ...errors }
    const newWarnings = { ...warnings }

    // Rimuovi errori/warning precedenti per questo cavo
    delete newErrors[cavoId]
    delete newWarnings[cavoId]

    if (isNaN(metri) || metri <= 0) {
      newErrors[cavoId] = 'Inserire un valore valido maggiore di 0'
    } else if (metri > metriTeorici * 1.1) {
      newErrors[cavoId] = `Metri eccessivi (max consigliato: ${(metriTeorici * 1.1).toFixed(1)}m)`
    } else if (metri > metriTeorici) {
      newWarnings[cavoId] = `Metri superiori ai teorici (${metriTeorici}m)`
    }

    setErrors(newErrors)
    setWarnings(newWarnings)
  }

  // Valida tutti i metri
  const validateAllMetri = (): boolean => {
    let isValid = true
    const newErrors: Record<string, string> = {}
    const newWarnings: Record<string, string> = {}

    caviSelezionati.forEach(cavo => {
      const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')
      const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0')

      if (isNaN(metri) || metri <= 0) {
        newErrors[cavo.id_cavo] = 'Inserire un valore valido maggiore di 0'
        isValid = false
      } else if (metri > metriTeorici * 1.1) {
        newErrors[cavo.id_cavo] = `Metri eccessivi (max consigliato: ${(metriTeorici * 1.1).toFixed(1)}m)`
        isValid = false
      } else if (metri > metriTeorici) {
        newWarnings[cavo.id_cavo] = `Metri superiori ai teorici (${metriTeorici}m)`
      }
    })

    setErrors(newErrors)
    setWarnings(newWarnings)

    return isValid
  }

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!cantiere || !bobina) return

    try {
      // Validazione
      if (!validateAllMetri()) {
        return
      }

      setSaving(true)

      // Aggiorna ogni cavo selezionato
      const results = []
      const errors = []

      for (const cavo of caviSelezionati) {
        try {
          const metriPosati = parseFloat(caviMetri[cavo.id_cavo])

          // Aggiorna i metri posati del cavo (logica semplice)
          await caviApi.updateMetriPosati(
            cantiere.id_cantiere,
            cavo.id_cavo,
            metriPosati,
            bobina.id_bobina
          )

          results.push({
            cavo: cavo.id_cavo,
            metriPosati,
            success: true
          })
        } catch (error: any) {
          console.error(`Errore aggiornamento cavo ${cavo.id_cavo}:`, error)
          const errorDetail = error.response?.data?.detail || error.message || 'Errore sconosciuto'

          // Gestione errori specifici
          let userFriendlyError = errorDetail
          if (errorDetail.includes('SPARE')) {
            userFriendlyError = `Cavo ${cavo.id_cavo} è marcato come SPARE`
          } else if (errorDetail.includes('metri posati non possono essere negativi')) {
            userFriendlyError = `Metri non validi per cavo ${cavo.id_cavo}`
          } else if (errorDetail.includes('non trovato')) {
            userFriendlyError = `Cavo ${cavo.id_cavo} non trovato`
          }

          errors.push({
            cavo: cavo.id_cavo,
            error: userFriendlyError
          })
        }
      }

      // Gestione del risultato
      if (errors.length === 0) {
        onSuccess(`${results.length} cavi aggiornati con successo`)
        onClose()
      } else if (results.length > 0) {
        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`)
        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)
        onClose()
      } else {
        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)
      }
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      onError('Errore durante il salvataggio dei cavi')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      setWarnings({})
      onClose()
    }
  }

  if (!bobina) return null

  const getBobinaNumber = (idBobina: string) => {
    const match = idBobina.match(/C\d+_B(\d+)/)
    return match ? match[1] : idBobina
  }

  // Renderizza la lista dei cavi
  const renderCaviList = (cavi: CavoConMetri[], isCompatible: boolean) => {
    if (cavi.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Cable className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <div>Nessun cavo {isCompatible ? 'compatibile' : 'incompatibile'} disponibile</div>
          <div className="text-xs mt-2 text-gray-400">
            {isCompatible
              ? `Cerca cavi con tipologia "${bobina?.tipologia}" e formazione "${bobina?.sezione}"`
              : 'I cavi incompatibili hanno tipologia o formazione diverse'
            }
          </div>
          <div className="text-xs mt-1 text-gray-400">
            Controlla la console del browser per dettagli di debug
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {cavi.map(cavo => {
          const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)
          const metri = caviMetri[cavo.id_cavo] || ''
          const hasError = errors[cavo.id_cavo]
          const hasWarning = warnings[cavo.id_cavo]

          return (
            <Card key={cavo.id_cavo} className={`p-3 ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
              <div className="flex items-start gap-3">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => handleCavoToggle(cavo, isCompatible)}
                  className="mt-1"
                />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">{cavo.id_cavo}</span>
                    <Badge variant="outline" className="text-xs">
                      {cavo.tipologia}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {cavo.sezione}
                    </Badge>
                    {!isCompatible && (
                      <Badge variant="destructive" className="text-xs">
                        Incompatibile
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-2 text-xs text-gray-600 mb-2">
                    <div>
                      <span className="font-medium">Teorici:</span> {cavo.metri_teorici}m
                    </div>
                    <div>
                      <span className="font-medium">Posati:</span> {cavo.metri_posati}m
                    </div>
                    <div>
                      <span className="font-medium">Bobina:</span> {cavo.id_bobina || 'Nessuna'}
                    </div>
                  </div>

                  {isSelected && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Label htmlFor={`metri-${cavo.id_cavo}`} className="text-xs">
                          Metri da posare:
                        </Label>
                        <Input
                          id={`metri-${cavo.id_cavo}`}
                          type="number"
                          step="0.1"
                          min="0"
                          value={metri}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                          className={`w-24 h-8 text-xs ${hasError ? 'border-red-500' : hasWarning ? 'border-yellow-500' : ''}`}
                          placeholder="0"
                        />
                        <span className="text-xs text-gray-500">m</span>
                      </div>

                      {hasError && (
                        <div className="text-xs text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {hasError}
                        </div>
                      )}

                      {hasWarning && !hasError && (
                        <div className="text-xs text-yellow-600 flex items-center gap-1">
                          <Info className="h-3 w-3" />
                          {hasWarning}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent 
          className="max-h-[95vh] overflow-y-auto"
          style={{ 
            width: '1200px !important', 
            maxWidth: '95vw !important',
            minWidth: '1200px'
          }}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Cable className="h-5 w-5" />
                Aggiungi cavi alla bobina {getBobinaNumber(bobina.id_bobina)}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDebugDialog(true)}
                className="flex items-center gap-1"
              >
                <Bug className="h-4 w-4" />
                Debug
              </Button>
            </DialogTitle>
            <DialogDescription>
              Seleziona i cavi da associare alla bobina e inserisci i metri posati
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Informazioni bobina */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Dettagli bobina</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <Label className="text-xs text-gray-600">Bobina</Label>
                    <div className="font-medium">{getBobinaNumber(bobina.id_bobina)}</div>
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Tipologia</Label>
                    <div className="font-medium">{bobina.tipologia}</div>
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Formazione</Label>
                    <div className="font-medium">{bobina.sezione}</div>
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Metri Residui</Label>
                    <div className="font-medium">{bobina.metri_residui}m</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Loading */}
            {caviLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Caricamento cavi...</span>
              </div>
            )}

            {/* Tabs per cavi compatibili/incompatibili */}
            {!caviLoading && (
              <Tabs defaultValue="compatibili" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="compatibili">
                    Cavi compatibili ({caviCompatibili.length})
                  </TabsTrigger>
                  <TabsTrigger value="incompatibili">
                    Cavi incompatibili ({caviIncompatibili.length})
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="compatibili" className="space-y-2">
                  <div className="text-sm text-gray-600 mb-2">
                    Cavi compatibili con tipologia <strong>{bobina.tipologia}</strong> e formazione <strong>{bobina.sezione}</strong>
                  </div>
                  {renderCaviList(caviCompatibili, true)}
                </TabsContent>

                <TabsContent value="incompatibili" className="space-y-2">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      I cavi incompatibili possono essere utilizzati con <strong>force_over</strong>,
                      ma potrebbero non rispettare le specifiche tecniche della bobina.
                    </AlertDescription>
                  </Alert>
                  {renderCaviList(caviIncompatibili, false)}
                </TabsContent>
              </Tabs>
            )}
          </div>

          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDebugDialog(true)}
                className="flex items-center gap-1"
              >
                <Bug className="h-4 w-4" />
                Debug
              </Button>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose} disabled={saving}>
                Annulla
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving || caviSelezionati.length === 0 || Object.keys(errors).length > 0}
              >
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {saving ? 'Salvataggio...' : `Salva ${caviSelezionati.length} cavi`}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog di debug */}
      <CaviDebugDialog
        open={showDebugDialog}
        onClose={() => setShowDebugDialog(false)}
        bobina={bobina}
      />
    </>
  )
}
