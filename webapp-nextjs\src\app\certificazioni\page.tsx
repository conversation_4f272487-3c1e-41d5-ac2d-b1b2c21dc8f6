'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { api } from '@/lib/api'
import { CertificazioneCavo } from '@/types'
import { 
  FileText, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Eye,
  Download,
  Upload,
  Loader2,
  Award
} from 'lucide-react'

export default function CertificazioniPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [certificazioni, setCertificazioni] = useState<CertificazioneCavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica le certificazioni dal backend
  useEffect(() => {
    loadCertificazioni()
  }, [])

  const loadCertificazioni = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const data = await api.get(`/certificazioni/${cantiereId}`)
      setCertificazioni(data)
    } catch (error: any) {
      console.error('Errore caricamento certificazioni:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento delle certificazioni')
    } finally {
      setIsLoading(false)
    }
  }

  const getResultBadge = (risultato: string) => {
    switch (risultato?.toLowerCase()) {
      case 'conforme':
      case 'pass':
      case 'ok':
        return <Badge className="bg-green-100 text-green-800">Conforme</Badge>
      case 'non conforme':
      case 'fail':
      case 'ko':
        return <Badge className="bg-red-100 text-red-800">Non Conforme</Badge>
      case 'in corso':
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">In Corso</Badge>
      default:
        return <Badge variant="secondary">{risultato || 'Da Verificare'}</Badge>
    }
  }

  const filteredCertificazioni = certificazioni.filter(cert => {
    const matchesSearch = cert.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.operatore?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.strumento_utilizzato?.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesStatus = true
    if (selectedStatus !== 'all') {
      switch (selectedStatus) {
        case 'conforme':
          matchesStatus = cert.risultato?.toLowerCase().includes('conforme') || cert.risultato?.toLowerCase() === 'pass'
          break
        case 'non_conforme':
          matchesStatus = cert.risultato?.toLowerCase().includes('non conforme') || cert.risultato?.toLowerCase() === 'fail'
          break
        case 'in_corso':
          matchesStatus = cert.risultato?.toLowerCase().includes('corso') || cert.risultato?.toLowerCase() === 'pending'
          break
      }
    }
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: certificazioni.length,
    conformi: certificazioni.filter(c => c.risultato?.toLowerCase().includes('conforme') || c.risultato?.toLowerCase() === 'pass').length,
    non_conformi: certificazioni.filter(c => c.risultato?.toLowerCase().includes('non conforme') || c.risultato?.toLowerCase() === 'fail').length,
    in_corso: certificazioni.filter(c => c.risultato?.toLowerCase().includes('corso') || c.risultato?.toLowerCase() === 'pending').length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              Certificazioni
            </h1>
            <p className="text-slate-600 mt-1">Gestione completa delle certificazioni e test dei cavi</p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Esporta
            </Button>
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Importa
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Nuova Certificazione
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Conformi</p>
                  <p className="text-2xl font-bold text-green-600">{stats.conformi}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Non Conformi</p>
                  <p className="text-2xl font-bold text-red-600">{stats.non_conformi}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Corso</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.in_corso}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Ricerca e Filtri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Cerca per ID cavo, operatore o strumento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'conforme', 'non_conforme', 'in_corso'].map((status) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'all' ? 'Tutte' : 
                     status === 'conforme' ? 'Conformi' :
                     status === 'non_conforme' ? 'Non Conformi' : 'In Corso'}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Certificazioni Table */}
        <Card>
          <CardHeader>
            <CardTitle>Elenco Certificazioni ({filteredCertificazioni.length})</CardTitle>
            <CardDescription>
              Gestione completa delle certificazioni con risultati e dettagli tecnici
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Cavo</TableHead>
                    <TableHead>Data Certificazione</TableHead>
                    <TableHead>Risultato</TableHead>
                    <TableHead>Operatore</TableHead>
                    <TableHead>Strumento</TableHead>
                    <TableHead>Note</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento certificazioni...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredCertificazioni.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                        Nessuna certificazione trovata
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCertificazioni.map((cert) => (
                      <TableRow key={cert.id_certificazione}>
                        <TableCell className="font-medium">{cert.id_cavo}</TableCell>
                        <TableCell>
                          {new Date(cert.data_certificazione).toLocaleDateString('it-IT')}
                        </TableCell>
                        <TableCell>{getResultBadge(cert.risultato)}</TableCell>
                        <TableCell>{cert.operatore || '-'}</TableCell>
                        <TableCell>{cert.strumento_utilizzato || '-'}</TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate" title={cert.note}>
                            {cert.note || '-'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Award className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
