{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "a91414678b226dc34c5ef50283ec0392", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "efe962eae82c399152fa9077e6671f1fac36686c285970aabc546684c0ca6f97", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3a6919d1d17071f7e9ef5f7633bc01cb9367ce0ae62077cd5d3d25270e7ee9b2"}}}, "sortedMiddleware": ["/"], "functions": {}}