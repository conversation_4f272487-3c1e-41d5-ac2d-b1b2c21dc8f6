'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON>ader2, <PERSON>ert<PERSON><PERSON>gle, Trash2 } from 'lucide-react'
import { parcoCaviApi } from '@/lib/api'

import { ParcoCavo } from '@/types'
import { canModifyReel, getReelStateDescription } from '@/utils/bobineUtils'

interface EliminaBobinaDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  cantiereId: number
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function EliminaBobinaDialog({
  open,
  onClose,
  bobina,
  cantiereId,
  onSuccess,
  onError
}: EliminaBobinaDialogProps) {
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    if (!bobina) return

    try {
      setLoading(true)

      if (!cantiereId || cantiereId <= 0) {
        throw new Error('Cantiere non selezionato')
      }

      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})
      const bobinaNumero = bobina.id_bobina.split('_B')[1]
      
      const response = await parcoCaviApi.deleteBobina(cantiereId, bobinaNumero)

      let message = `Bobina ${bobina.numero_bobina} eliminata con successo`
      
      // Se è l'ultima bobina, aggiungi informazione aggiuntiva
      if (response.data?.is_last_bobina) {
        message += '. Era l\'ultima bobina del cantiere.'
      }

      onSuccess(message)
      onClose()
    } catch (error: any) {
      console.error('Errore nell\'eliminazione bobina:', error)
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'eliminazione della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      onClose()
    }
  }

  if (!bobina) return null

  // Verifica se la bobina può essere eliminata
  const canDelete = canModifyReel(bobina.stato_bobina) && 
                   bobina.metri_residui === bobina.metri_totali

  // Determina il tipo di avviso da mostrare
  const getWarningInfo = () => {
    if (!canModifyReel(bobina.stato_bobina)) {
      return {
        type: 'error' as const,
        title: 'Eliminazione non consentita',
        message: `La bobina è in stato "${bobina.stato_bobina}" e non può essere eliminata. ${getReelStateDescription(bobina.stato_bobina)}`
      }
    }

    if (bobina.metri_residui !== bobina.metri_totali) {
      return {
        type: 'error' as const,
        title: 'Bobina in uso',
        message: `La bobina ha ${bobina.metri_residui}m residui su ${bobina.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`
      }
    }

    return {
      type: 'warning' as const,
      title: 'Conferma eliminazione',
      message: 'Questa operazione è irreversibile. La bobina verrà rimossa definitivamente dal parco cavi.'
    }
  }

  const warningInfo = getWarningInfo()

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Elimina Bobina
          </DialogTitle>
          <DialogDescription>
            Stai per eliminare la bobina {bobina.numero_bobina}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Informazioni bobina */}
          <div className="bg-slate-50 p-4 rounded-lg mb-4">
            <h4 className="font-medium mb-2">Dettagli bobina:</h4>
            <div className="text-sm space-y-1">
              <div><strong>Bobina:</strong> {bobina.numero_bobina}</div>
              <div><strong>Utility:</strong> {bobina.utility}</div>
              <div><strong>Tipologia:</strong> {bobina.tipologia}</div>
              <div><strong>Sezione:</strong> {bobina.sezione}</div>
              <div><strong>Stato:</strong> {bobina.stato_bobina}</div>
              <div><strong>Metri:</strong> {bobina.metri_residui}m / {bobina.metri_totali}m</div>
              {bobina.ubicazione_bobina && (
                <div><strong>Ubicazione:</strong> {bobina.ubicazione_bobina}</div>
              )}
            </div>
          </div>

          {/* Avviso */}
          <Alert variant={warningInfo.type === 'error' ? 'destructive' : 'default'}>
            <AlertTriangle className="h-4 w-4" />
            <div>
              <div className="font-medium">{warningInfo.title}</div>
              <AlertDescription className="mt-1">
                {warningInfo.message}
              </AlertDescription>
            </div>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={loading || !canDelete}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Eliminando...' : 'Elimina Bobina'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
