'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle } from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface Bobina {
  id_bobina: string
  tipologia: string
  formazione: string
  metri_residui: number
  fornitore?: string
}

interface InserisciMetriDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function InserisciMetriDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: InserisciMetriDialogProps) {
  const { cantiere } = useAuth()
  const [metriPosati, setMetriPosati] = useState('')
  const [selectedBobina, setSelectedBobina] = useState('')
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [error, setError] = useState('')

  // Carica bobine compatibili quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      loadBobineCompatibili()
      setMetriPosati(cavo.metri_teorici?.toString() || '')
      setSelectedBobina('')
      setError('')
    }
  }, [open, cavo])

  const loadBobineCompatibili = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoadingBobine(true)

      // Carica bobine compatibili dall'API - STANDARDIZZAZIONE: usa formazione
      const response = await parcoCaviApi.getBobineCompatibili(cantiere.id_cantiere, {
        tipologia: cavo.tipologia,
        n_conduttori: cavo.n_conduttori,
        formazione: cavo.formazione || cavo.sezione, // Priorità a formazione
        sezione: cavo.formazione || cavo.sezione     // Fallback per compatibilità
      })

      const bobineCompatibili: Bobina[] = response.data.map((b: any) => ({
        id_bobina: b.id_bobina,
        tipologia: b.tipologia,
        formazione: b.sezione || b.formazione, // Bobina usa ancora 'sezione' nel DB
        metri_residui: b.metri_residui,
        fornitore: b.fornitore
      }))

      // Aggiungi sempre BOBINA_VUOTA come opzione
      const bobineConVuota: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          formazione: cavo.formazione || cavo.sezione || '',
          metri_residui: 0
        },
        ...bobineCompatibili
      ]

      setBobine(bobineConVuota)
    } catch (error) {
      console.error('Errore nel caricamento bobine:', error)
      // Fallback con solo BOBINA_VUOTA
      setBobine([{
        id_bobina: 'BOBINA_VUOTA',
        tipologia: cavo.tipologia || '',
        formazione: cavo.formazione || cavo.sezione || '',
        metri_residui: 0
      }])
      onError('Errore nel caricamento delle bobine disponibili')
    } finally {
      setLoadingBobine(false)
    }
  }

  const handleSave = async () => {
    if (!cavo || !metriPosati || !selectedBobina) {
      setError('Compilare tutti i campi obbligatori')
      return
    }

    const metri = parseFloat(metriPosati)
    if (isNaN(metri) || metri <= 0) {
      setError('Inserire un valore valido per i metri posati')
      return
    }

    if (metri > (cavo.metri_teorici || 0)) {
      setError('I metri posati non possono superare i metri teorici')
      return
    }

    // Verifica metri disponibili nella bobina (se non è BOBINA_VUOTA)
    const bobina = bobine.find(b => b.id_bobina === selectedBobina)
    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && metri > bobina.metri_residui) {
      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili`)
      return
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Aggiorna metri posati tramite API
      await caviApi.updateMetriPosati(
        cantiere.id_cantiere,
        cavo.id_cavo,
        metri,
        selectedBobina !== 'BOBINA_VUOTA' ? selectedBobina : undefined
      )

      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)
      onClose()
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setMetriPosati('')
      setSelectedBobina('')
      setError('')
      onClose()
    }
  }

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Inserisci Metri Posati</DialogTitle>
          <DialogDescription>
            Inserisci i metri posati per il cavo {cavo.id_cavo}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informazioni cavo */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>Sistema:</strong> {cavo.sistema}</div>
              <div><strong>Utility:</strong> {cavo.utility}</div>
              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>
              <div><strong>Formazione:</strong> {cavo.formazione}</div>
              <div><strong>Da:</strong> {cavo.da}</div>
              <div><strong>A:</strong> {cavo.a}</div>
              <div className="col-span-2">
                <strong>Metri teorici:</strong> {cavo.metri_teorici}m
              </div>
            </div>
          </div>

          {/* Metri posati */}
          <div className="space-y-2">
            <Label htmlFor="metri">Metri Posati *</Label>
            <Input
              id="metri"
              type="number"
              step="0.1"
              min="0"
              max={cavo.metri_teorici || 0}
              value={metriPosati}
              onChange={(e) => setMetriPosati(e.target.value)}
              placeholder="Inserisci metri posati"
            />
          </div>

          {/* Selezione bobina */}
          <div className="space-y-2">
            <Label htmlFor="bobina">Bobina *</Label>
            {loadingBobine ? (
              <div className="flex items-center space-x-2 p-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Caricamento bobine...</span>
              </div>
            ) : (
              <Select value={selectedBobina} onValueChange={setSelectedBobina}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleziona bobina" />
                </SelectTrigger>
                <SelectContent>
                  {bobine.map((bobina) => (
                    <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>
                      <div className="flex flex-col">
                        <span>{bobina.id_bobina}</span>
                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (
                          <span className="text-xs text-muted-foreground">
                            {bobina.metri_residui}m disponibili
                            {bobina.fornitore && ` - ${bobina.fornitore}`}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Errori */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={loading || !metriPosati || !selectedBobina}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Salvando...' : 'Salva'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
