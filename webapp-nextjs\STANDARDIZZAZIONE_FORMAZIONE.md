# 🔧 Standardizzazione SEZIONE → FORMAZIONE

## 📋 **Panoramica**

Il sistema CMS sta migrando dal campo obsoleto **`sezione`** al nuovo campo **`formazione`** per una migliore gestione della compatibilità tra cavi e bobine.

## 🎯 **Obiettivi**

1. **Standardizzare** tutti i riferimenti su `formazione`
2. **Mantenere compatibilità** temporanea con `sezione` esistente
3. **Migliorare ricerche** e filtri di compatibilità
4. **Unificare logica** tra frontend e backend

## ✅ **Modifiche Implementate**

### **Frontend Next.js**

#### **1. Componenti Aggiornati**
- ✅ `AggiungiCaviDialog.tsx` - Logica compatibilità standardizzata
- ✅ `InserisciMetriDialog.tsx` - API calls con formazione
- ✅ `ModificaBobinaDialog.tsx` - Mapping bobine con formazione
- ✅ `CaviDebugDialog.tsx` - Debug con entrambi i campi

#### **2. API Calls**
- ✅ `parcoCaviApi.getBobineCompatibili()` - Parametri formazione + sezione
- ✅ `parcoCaviApi.getBobine()` - Filtri aggiornati

#### **3. Logica di Compatibilità**
```typescript
// PRIMA (solo sezione)
const isCompatible = cavo.tipologia === bobina.tipologia && 
                    cavo.sezione === bobina.sezione

// DOPO (formazione con fallback)
const cavoFormazione = cavo.formazione || cavo.sezione || ''
const bobinaFormazione = bobina.sezione || bobina.formazione || ''
const isCompatible = cavoTipologia === bobinaTipologia && 
                    cavoFormazioneNorm === bobinaFormazioneNorm
```

### **4. Debug e Diagnostica**
- ✅ Logging dettagliato per entrambi i campi
- ✅ Componente debug dedicato con analisi compatibilità
- ✅ Messaggi informativi migliorati

## 🔄 **Strategia di Migrazione**

### **Fase 1: Compatibilità Doppia (ATTUALE)**
- Frontend usa `formazione` con fallback a `sezione`
- Backend mantiene entrambi i campi
- API accetta entrambi i parametri

### **Fase 2: Migrazione Database (FUTURA)**
- Aggiornare modelli database per usare `formazione`
- Migrare dati esistenti da `sezione` a `formazione`
- Aggiornare schemi Pydantic

### **Fase 3: Cleanup (FINALE)**
- Rimuovere riferimenti a `sezione`
- Semplificare logica di compatibilità
- Aggiornare documentazione

## 🔍 **Logica di Compatibilità Attuale**

### **Per i Cavi:**
```typescript
const cavoFormazione = cavo.formazione || cavo.sezione || ''
```

### **Per le Bobine:**
```typescript
const bobinaFormazione = bobina.sezione || bobina.formazione || ''
```
*Nota: Le bobine usano ancora `sezione` nel database*

### **Confronto Normalizzato:**
```typescript
const cavoFormazioneNorm = cavoFormazione.trim().toUpperCase()
const bobinaFormazioneNorm = bobinaFormazione.trim().toUpperCase()
const isCompatible = cavoTipologia === bobinaTipologia && 
                    cavoFormazioneNorm === bobinaFormazioneNorm
```

## 🚨 **Problemi Risolti**

1. **Cavi non visualizzati** in "Aggiungi cavi alla bobina"
2. **Incompatibilità false** per differenze di case/spazi
3. **Confusione campo** sezione vs formazione
4. **Debug insufficiente** per diagnosticare problemi

## 📊 **Componenti Debug**

### **CaviDebugDialog**
- Analisi completa del filtro cavi
- Statistiche per categoria (esclusi, disponibili, compatibili)
- Dettagli motivi esclusione
- Confronto tipologia/formazione

### **Logging Console**
```javascript
console.log(`🔍 DEBUG: Compatibilità cavo ${cavo.id_cavo}:`, {
  cavo_tipologia: cavoTipologia,
  bobina_tipologia: bobinaTipologia,
  cavo_formazione: cavoFormazioneNorm,
  bobina_formazione: bobinaFormazioneNorm,
  cavo_formazione_raw: cavo.formazione,
  cavo_sezione_raw: cavo.sezione,
  bobina_sezione_raw: bobina?.sezione,
  bobina_formazione_raw: bobina?.formazione,
  tipologia_match: cavoTipologia === bobinaTipologia,
  formazione_match: cavoFormazioneNorm === bobinaFormazioneNorm
})
```

## 🔮 **Prossimi Passi**

1. **Testare** la compatibilità con dati reali
2. **Verificare** che tutti i cavi vengano visualizzati correttamente
3. **Monitorare** i log di debug per identificare altri problemi
4. **Pianificare** migrazione database per Fase 2

## 📝 **Note per Sviluppatori**

- **Sempre usare** `formazione` come campo primario
- **Mantenere fallback** a `sezione` per compatibilità
- **Normalizzare valori** prima dei confronti (trim + uppercase)
- **Loggare dettagli** per debug quando necessario
- **Testare compatibilità** con entrambi i campi

## 🎯 **Risultato Atteso**

Dopo queste modifiche, il modulo "Aggiungi cavi alla bobina" dovrebbe:
- ✅ Visualizzare tutti i cavi disponibili
- ✅ Calcolare correttamente la compatibilità
- ✅ Fornire debug dettagliato in caso di problemi
- ✅ Gestire sia `formazione` che `sezione` trasparentemente
