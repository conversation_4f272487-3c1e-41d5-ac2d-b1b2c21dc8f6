'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Package } from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface Bobina {
  id_bobina: string
  tipologia: string
  formazione: string
  metri_residui: number
  fornitore?: string
}

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere } = useAuth()
  const [selectedBobina, setSelectedBobina] = useState('')
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [error, setError] = useState('')

  // Carica bobine compatibili quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      loadBobineCompatibili()
      setSelectedBobina(cavo.id_bobina || '')
      setError('')
    }
  }, [open, cavo])

  const loadBobineCompatibili = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoadingBobine(true)

      // Carica bobine compatibili dall'API - STANDARDIZZAZIONE: usa formazione
      const response = await parcoCaviApi.getBobineCompatibili(cantiere.id_cantiere, {
        tipologia: cavo.tipologia,
        n_conduttori: cavo.n_conduttori,
        formazione: cavo.formazione || cavo.sezione, // Priorità a formazione
        sezione: cavo.formazione || cavo.sezione     // Fallback per compatibilità
      })

      const bobineCompatibili: Bobina[] = response.data.map((b: any) => ({
        id_bobina: b.id_bobina,
        tipologia: b.tipologia,
        formazione: b.sezione || b.formazione, // Bobina usa ancora 'sezione' nel DB
        metri_residui: b.metri_residui,
        fornitore: b.fornitore
      }))

      // Aggiungi sempre BOBINA_VUOTA come opzione
      const bobineConVuota: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          formazione: cavo.formazione || cavo.sezione || '',
          metri_residui: 0
        },
        ...bobineCompatibili
      ]

      // Se il cavo ha già una bobina, assicurati che sia nella lista
      if (cavo.id_bobina && !bobineConVuota.find(b => b.id_bobina === cavo.id_bobina)) {
        bobineConVuota.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          formazione: cavo.formazione || cavo.sezione || '',
          metri_residui: 0 // Bobina attualmente in uso
        })
      }

      setBobine(bobineConVuota)
    } catch (error) {
      console.error('Errore nel caricamento bobine:', error)
      // Fallback con solo BOBINA_VUOTA e bobina corrente
      const fallbackBobine: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          formazione: cavo.formazione || cavo.sezione || '',
          metri_residui: 0
        }
      ]

      if (cavo.id_bobina) {
        fallbackBobine.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          formazione: cavo.formazione || cavo.sezione || '',
          metri_residui: 0
        })
      }

      setBobine(fallbackBobine)
      onError('Errore nel caricamento delle bobine disponibili')
    } finally {
      setLoadingBobine(false)
    }
  }

  const handleSave = async () => {
    if (!cavo || !selectedBobina) {
      setError('Selezionare una bobina')
      return
    }

    if (selectedBobina === cavo.id_bobina) {
      setError('La bobina selezionata è già associata al cavo')
      return
    }

    // Verifica che la bobina abbia metri sufficienti (se non è BOBINA_VUOTA)
    const bobina = bobine.find(b => b.id_bobina === selectedBobina)
    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && cavo.metri_posati > bobina.metri_residui) {
      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili, ma il cavo ha ${cavo.metri_posati}m posati`)
      return
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Aggiorna bobina tramite API
      await caviApi.updateBobina(
        cantiere.id_cantiere,
        cavo.id_cavo,
        selectedBobina
      )

      const message = selectedBobina === 'BOBINA_VUOTA'
        ? `Bobina vuota assegnata al cavo ${cavo.id_cavo}`
        : `Bobina ${selectedBobina} assegnata al cavo ${cavo.id_cavo}`

      onSuccess(message)
      onClose()
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setSelectedBobina('')
      setError('')
      onClose()
    }
  }

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Modifica Bobina</span>
          </DialogTitle>
          <DialogDescription>
            Modifica la bobina associata al cavo {cavo.id_cavo}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informazioni cavo */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>Sistema:</strong> {cavo.sistema}</div>
              <div><strong>Utility:</strong> {cavo.utility}</div>
              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>
              <div><strong>Formazione:</strong> {cavo.formazione}</div>
              <div><strong>Metri posati:</strong> {cavo.metri_posati || 0}m</div>
              <div><strong>Bobina attuale:</strong> {cavo.id_bobina || 'Nessuna'}</div>
            </div>
          </div>

          {/* Selezione nuova bobina */}
          <div className="space-y-2">
            <Label htmlFor="bobina">Nuova Bobina *</Label>
            {loadingBobine ? (
              <div className="flex items-center space-x-2 p-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Caricamento bobine...</span>
              </div>
            ) : (
              <Select value={selectedBobina} onValueChange={setSelectedBobina}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleziona nuova bobina" />
                </SelectTrigger>
                <SelectContent>
                  {bobine.map((bobina) => (
                    <SelectItem 
                      key={bobina.id_bobina} 
                      value={bobina.id_bobina}
                      disabled={bobina.id_bobina === cavo.id_bobina}
                    >
                      <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                          <span>{bobina.id_bobina}</span>
                          {bobina.id_bobina === cavo.id_bobina && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                              Attuale
                            </span>
                          )}
                        </div>
                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (
                          <span className="text-xs text-muted-foreground">
                            {bobina.metri_residui}m disponibili
                            {bobina.fornitore && ` - ${bobina.fornitore}`}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Avviso per bobina vuota */}
          {selectedBobina === 'BOBINA_VUOTA' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Stai assegnando una bobina vuota. Questo permetterà di posare il cavo 
                e associare la bobina reale in un secondo momento.
              </AlertDescription>
            </Alert>
          )}

          {/* Errori */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={loading || !selectedBobina || selectedBobina === cavo.id_bobina}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Salvando...' : 'Salva'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
